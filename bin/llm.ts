import assert from "assert";
import <PERSON>A<PERSON> from "openai";

import ComposeLoglinesJob from "../src/jobs/ComposeLoglinesJob.js";
import OpenAILoglineComposer from "../src/openai/OpenAILoglineComposer.js";
import ConnectionPool from "../src/postgres/ConnectionPool.js";
import PostgresEventHandler from "../src/postgres/PostgresEventHandler.js";
import PostgresLoglineJobQueue from "../src/postgres/PostgresLoglineJobQueue.js";
import PostgresLoglineRepository from "../src/postgres/PostgresLoglineRepository.js";

async function runScript(): Promise<void> {
  assert(
    process.env.POSTGRES_CONNECTION_STRING,
    "Missing POSTGRES_CONNECTION_STRING env variable",
  );
  assert(process.env.DEEPSEEK_API_KEY, "Missing DEEPSEEK_API_KEY env variable");

  debug("LLM");

  const pool = new ConnectionPool(process.env.POSTGRES_CONNECTION_STRING);
  const loglineRepository = new PostgresLoglineRepository(pool);
  const externalStorage = new PostgresLoglineJobQueue(pool);
  const loglineComposer = new OpenAILoglineComposer(
    new OpenAI({
      baseURL: "https://api.deepseek.com",
      apiKey: process.env.DEEPSEEK_API_KEY,
    }),
    "deepseek-chat",
  );
  const eventBus = new PostgresEventHandler(debug, pool);
  const service = new ComposeLoglinesJob(
    eventBus,
    loglineRepository,
    externalStorage,
    loglineComposer,
    debug,
  );

  await service.run();
  await eventBus.batch();

  debug("Done");
}

runScript().catch((error: unknown) => {
  debug(error instanceof Error ? error.stack ?? error.message : String(error));
  process.exit(1);
});

function debug(message: string): void {
  process.stderr.write(
    `[${new Date().toTimeString().slice(0, 8)}] ${message}\n`,
  );
}
