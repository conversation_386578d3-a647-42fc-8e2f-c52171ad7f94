/* eslint-disable max-classes-per-file */
import { Writable } from "node:stream";
import isEqual from "lodash.isequal";

import EventBus from "../domain/EventBus.js";

export default class PullKinopoiskMarksJob {
  constructor(
    private debug: (message: string) => void,
    private eventBus: EventBus<Event>,
    private queue: JobQueue,
    private kinopoiskGateway: KinopoiskGateway,
    private movieRepository: MovieRepository,
    private userRepository: UserRepository,
  ) {}

  async refreshUsers(options: RefreshUsersOptions): Promise<void> {
    let jobs;

    if (options.url) {
      const id = /https:\/\/www.kinopoisk.ru\/user\/(\d+)\//.exec(
        options.url,
      )?.[1];

      if (!id) {
        throw new Error(`Invalid URL: ${options.url}`);
      }

      jobs = await this.queue.getJobsQueue({
        id,
        keepPreviousFriends: options.keepPreviousFriends,
      });
    } else {
      jobs = await this.queue.getJobsQueue(options);
    }

    this.debug(
      jobs.length === 1
        ? `Scheduled 1 update`
        : `Scheduled ${jobs.length} updates`,
    );

    for (const job of jobs) {
      this.debug(`${job.name ?? "unknown"} (${job.id})`);

      let user = await this.userRepository.find(job.id);
      const userUpdate = await this.kinopoiskGateway.fetchUser(job);

      if (!userUpdate) {
        this.debug(`Skipped due to error`);
        continue;
      }

      const movies = await this.movieRepository.findMany(
        userUpdate.marks.map((mark) => mark.movie.id),
      );
      const hash = new Map(
        movies.filter((x): x is Movie => x !== null).map((m) => [m.id, m]),
      );
      const now = new Date();
      const updatedMovies = userUpdate.marks.map((mark) => {
        const movie = hash.get(mark.movie.id);

        return movie
          ? mark.timestamp > movie.updatedAt
            ? updateMovie(this.eventBus, movie, mark.movie, now)
            : movie
          : createMovie(this.eventBus, mark.movie, now);
      });

      await this.movieRepository.setMany(updatedMovies);

      if (user) {
        user = updateUser(this.eventBus, user, job, userUpdate);
      } else {
        user = createUser(this.eventBus, userUpdate);
      }

      await this.userRepository.set(user);
    }
  }
}

export interface RefreshUsersOptions {
  keepPreviousFriends: boolean;
  url?: string;
}

export interface JobQueueOptions {
  id?: string;
  keepPreviousFriends: boolean;
}

export interface MovieCreatedEvent {
  payload: {
    id: string;
    title: string;
  };
  type: "MovieCreatedEvent";
}

export interface MovieTitleUpdatedEvent {
  payload: {
    id: string;
    prevTitle: string;
    title: string;
  };
  type: "MovieTitleUpdatedEvent";
}

export interface MovieYearUpdatedEvent {
  payload: {
    id: string;
    title: string;
    prevYear: number | null;
    year: number | null;
  };
  type: "MovieYearUpdatedEvent";
}

export interface UserCreatedEvent {
  payload: {
    id: string;
    name: string;
  };
  type: "UserCreatedEvent";
}

export interface UserMovieMarkCreatedEvent {
  payload: {
    user: {
      id: string;
      name: string;
    };
    movie: {
      id: string;
    };
    score: number | null;
    timestamp: Date;
  };
  type: "UserMovieMarkCreatedEvent";
}

export interface UserMovieMarkUpdatedEvent {
  payload: {
    user: {
      id: string;
      name: string;
    };
    movie: {
      id: string;
    };
    score: number | null;
    timestamp: Date;
  };
  type: "UserMovieMarkUpdatedEvent";
}

export interface UserMovieMarkDeletedEvent {
  payload: {
    user: {
      id: string;
      name: string;
    };
    movie: {
      id: string;
    };
  };
  type: "UserMovieMarkDeletedEvent";
}

export type Event =
  | MovieCreatedEvent
  | MovieTitleUpdatedEvent
  | MovieYearUpdatedEvent
  | UserCreatedEvent
  | UserMovieMarkCreatedEvent
  | UserMovieMarkUpdatedEvent
  | UserMovieMarkDeletedEvent;

export interface Movie {
  id: string;
  title: string;
  year: number | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface Mark {
  movie: {
    id: string;
  };
  score: number | null;
  timestamp: Date;
  scrapedAt: Date;
}

export interface Friend {
  id: string;
  name: string;
}

export interface User {
  id: string;
  friends: Friend[];
  marks: Mark[];
  name: string;
}

export interface CreateMovieArgs {
  id: string;
  title: string;
  year: number | null;
}

export function createMovie(
  eventBus: EventBus<Event>,
  create: CreateMovieArgs,
  now: Date,
): Movie {
  const movie: Movie = {
    ...create,
    createdAt: now,
    updatedAt: now,
  };
  const event: MovieCreatedEvent = {
    payload: {
      id: movie.id,
      title: movie.title,
    },
    type: "MovieCreatedEvent",
  };

  eventBus.push(event);

  return movie;
}

export interface UpdateMovieArgs {
  id: string;
  title: string;
  year: number | null;
}

export function updateMovie(
  eventBus: EventBus<Event>,
  initialMovie: Movie,
  update: UpdateMovieArgs,
  now: Date,
): Movie {
  let movie: Movie = initialMovie;

  if (movie.title !== update.title) {
    movie = {
      ...movie,
      title: update.title,
      updatedAt: now,
    };

    const event: MovieTitleUpdatedEvent = {
      payload: {
        id: movie.id,
        prevTitle: initialMovie.title,
        title: movie.title,
      },
      type: "MovieTitleUpdatedEvent",
    };

    eventBus.push(event);
  }

  if (movie.year !== update.year) {
    movie = {
      ...movie,
      year: update.year,
      updatedAt: now,
    };

    const event: MovieYearUpdatedEvent = {
      payload: {
        id: movie.id,
        title: movie.title,
        prevYear: initialMovie.year,
        year: update.year,
      },
      type: "MovieYearUpdatedEvent",
    };

    eventBus.push(event);
  }

  return movie;
}

export interface CreateUserArgs {
  id: string;
  friends: {
    id: string;
    name: string;
  }[];
  marks: {
    movie: {
      id: string;
    };
    score: number | null;
    timestamp: Date;
    scrapedAt: Date;
  }[];
  name: string;
}

export function createUser(
  eventBus: EventBus<Event>,
  create: CreateUserArgs,
): User {
  const user: User = create;
  const event: UserCreatedEvent = {
    payload: {
      id: create.id,
      name: create.name,
    },
    type: "UserCreatedEvent",
  };

  eventBus.push(event);

  for (const mark of user.marks) {
    eventBus.push({
      payload: {
        user: {
          id: user.id,
          name: user.name,
        },
        movie: {
          id: mark.movie.id,
        },
        score: mark.score,
        timestamp: mark.timestamp,
      },
      type: "UserMovieMarkCreatedEvent",
    });
  }

  return user;
}

export interface UpdateUserArgs {
  id: string;
  friends: {
    id: string;
    name: string;
  }[];
  marks: {
    movie: {
      id: string;
    };
    score: number | null;
    timestamp: Date;
    scrapedAt: Date;
  }[];
  name: string;
}

export function updateUser(
  eventBus: EventBus<Event>,
  user: User,
  job: KinopoiskJob,
  update: UpdateUserArgs,
): User {
  const updated: User = {
    id: update.id,
    friends: job.friends == null ? user.friends : update.friends,
    marks: job.marks.after
      ? uniqBy((mark) => mark.movie.id, [...update.marks, ...user.marks])
      : update.marks,
    name: update.name,
  };

  if (!isEqual(user.marks, updated.marks)) {
    const changeset = getMarksChangeset(user.marks, updated.marks);

    for (const mark of changeset.created) {
      const event: UserMovieMarkCreatedEvent = {
        payload: {
          user: {
            id: user.id,
            name: user.name,
          },
          movie: {
            id: mark.movie.id,
          },
          score: mark.score,
          timestamp: mark.timestamp,
        },
        type: "UserMovieMarkCreatedEvent",
      };

      eventBus.push(event);
    }

    for (const mark of changeset.updated) {
      const event: UserMovieMarkUpdatedEvent = {
        payload: {
          user: {
            id: user.id,
            name: user.name,
          },
          movie: {
            id: mark.movie.id,
          },
          score: mark.score,
          timestamp: mark.timestamp,
        },
        type: "UserMovieMarkUpdatedEvent",
      };

      eventBus.push(event);
    }

    for (const mark of changeset.deleted) {
      const event: UserMovieMarkDeletedEvent = {
        payload: {
          user: {
            id: user.id,
            name: user.name,
          },
          movie: {
            id: mark.movie.id,
          },
        },
        type: "UserMovieMarkDeletedEvent",
      };

      eventBus.push(event);
    }
  }

  return updated;
}

function getMarksChangeset(
  prevMarks: Mark[],
  nextMarks: Mark[],
): { created: Mark[]; updated: Mark[]; deleted: Mark[] } {
  const created: Mark[] = [];
  const updated: Mark[] = [];
  const deleted: Mark[] = [];

  const prevTimestamps: Record<Movie["id"], Date> = {};

  for (const mark of prevMarks) {
    prevTimestamps[mark.movie.id] = mark.timestamp;
  }

  const nextTimestamps: Record<Movie["id"], Date> = {};

  for (const mark of nextMarks) {
    nextTimestamps[mark.movie.id] = mark.timestamp;
  }

  for (const nextMark of nextMarks) {
    const prevTimestamp = prevTimestamps[nextMark.movie.id];

    if (!prevTimestamp) {
      created.push(nextMark);
    } else if (!isEqual(nextMark.timestamp, prevTimestamp)) {
      updated.push(nextMark);
    }
  }

  for (const prevMark of prevMarks) {
    const nextTimestamp = nextTimestamps[prevMark.movie.id];

    if (!nextTimestamp) {
      deleted.push(prevMark);
    }
  }

  return { created, updated, deleted };
}

function uniqBy<T>(fn: (t: T) => string, items: T[]): T[] {
  const hashes = new Set<string>();
  const result: T[] = [];

  items.forEach((item) => {
    const hash = fn(item);

    if (!hashes.has(hash)) {
      result.push(item);
      hashes.add(hash);
    }
  });

  return result;
}

export interface UserRepository {
  find(profileUrl: string): Promise<User | null>;
  set(user: User): Promise<void>;
}

export interface MovieRepository {
  findMany(kinopoiskUrls: string[]): Promise<(Movie | null)[]>;
  setMany(movies: Movie[]): Promise<void>;
}

export interface JobQueue {
  getJobsQueue(options: JobQueueOptions): Promise<KinopoiskJob[]>;
}

export interface KinopoiskGateway {
  fetchUser(
    job: KinopoiskJob,
  ): Promise<KinopoiskGatewayFetchUserResponse | null>;
}

export interface KinopoiskGatewayFetchUserResponse {
  id: string;
  friends: {
    id: string;
    name: string;
  }[];
  marks: {
    movie: {
      id: string;
      title: string;
      year: number | null;
    };
    score: number | null;
    timestamp: Date;
    scrapedAt: Date;
  }[];
  name: string;
}

export interface KinopoiskJob {
  id: string;
  name?: string;
  friends?: {
    limit: number;
  };
  marks: {
    after?: Date;
  };
}

export class EventBusLogger implements EventBus<Event> {
  constructor(private wstream: Writable) {}

  push(event: Event): void {
    if (event.type === "MovieCreatedEvent") {
      this.wstream.write(
        `[${new Date().toTimeString().slice(0, 8)}] Movie "${
          event.payload.title
        }" has been created (id: ${event.payload.id})\n`,
      );
    } else if (event.type === "MovieTitleUpdatedEvent") {
      this.wstream.write(
        `[${new Date().toTimeString().slice(0, 8)}] Movie "${
          event.payload.prevTitle
        }" title changed to "${event.payload.title}" (id: ${
          event.payload.id
        })\n`,
      );
    } else if (event.type === "MovieYearUpdatedEvent") {
      this.wstream.write(
        `[${new Date().toTimeString().slice(0, 8)}] Movie "${
          event.payload.title
        }" year changed from ${event.payload.prevYear} to ${
          event.payload.year
        } (id: ${event.payload.id})\n`,
      );
    } else if (event.type === "UserCreatedEvent") {
      this.wstream.write(
        `[${new Date().toTimeString().slice(0, 8)}] User "${
          event.payload.name
        }" has been created (id: ${event.payload.id})\n`,
      );
    } else if (
      event.type === "UserMovieMarkCreatedEvent" ||
      event.type === "UserMovieMarkUpdatedEvent"
    ) {
      if (event.payload.score === null) {
        this.wstream.write(
          `[${new Date().toTimeString().slice(0, 8)}] User "${
            event.payload.user.name
          }" (id: ${event.payload.user.id}) seen movie (id: ${
            event.payload.movie.id
          })\n`,
        );
      } else {
        this.wstream.write(
          `[${new Date().toTimeString().slice(0, 8)}] User "${
            event.payload.user.name
          }" (id: ${event.payload.user.id}) rated movie (id: ${
            event.payload.movie.id
          }) with ${event.payload.score}\n`,
        );
      }
    }
  }
}
