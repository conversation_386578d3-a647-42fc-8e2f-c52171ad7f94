import { Movie, MovieRepository } from "../jobs/PullTmdbContentJob.js";
import ConnectionPool from "./ConnectionPool.js";
import sql from "./sql.js";

export default class PostgresTmdbMovieRepository implements MovieRepository {
  constructor(private pool: ConnectionPool) {}

  async findMany(tmdbIds: string[]): Promise<(Movie | null)[]> {
    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{
        id: number;
        images_idxs: number[] | null;
        images_urls: string[] | null;
        images_widths: number[] | null;
        images_heights: number[] | null;
        images_types: string[] | null;
        genres_ids: number[] | null;
        keyword_ids: number[] | null;
      }>(sql`
        SELECT
          id,
          images.idxs as images_idxs,
          images.urls as images_urls,
          images.widths as images_widths,
          images.heights as images_heights,
          images.types as images_types,
          genres.ids as genres_ids,
          keyword.ids as keyword_ids
        FROM
          tmdb_movie
        LEFT JOIN LATERAL
          (
            SELECT
              ARRAY_AGG(tmdb_movie_image.order ORDER BY "order" ASC, width ASC, url DESC) as idxs,
              ARRAY_AGG(tmdb_movie_image.url ORDER BY "order" ASC, width ASC, url DESC) as urls,
              ARRAY_AGG(tmdb_movie_image.width ORDER BY "order" ASC, width ASC, url DESC) as widths,
              ARRAY_AGG(tmdb_movie_image.height ORDER BY "order" ASC, width ASC, url DESC) as heights,
              ARRAY_AGG(tmdb_movie_image.type ORDER BY "order" ASC, width ASC, url DESC) as types
            FROM
              tmdb_movie_image
            WHERE
              tmdb_movie_image.movie_id = tmdb_movie.id
          ) images ON true
        LEFT JOIN LATERAL
          (
            SELECT
              ARRAY_AGG(tmdb_movie_genre.genre_id ORDER BY tmdb_movie_genre."order" ASC) as ids
            FROM
              tmdb_movie_genre
            WHERE
              tmdb_movie_genre.movie_id = tmdb_movie.id
          ) genres ON true
        LEFT JOIN LATERAL
          (
            SELECT
              ARRAY_AGG(tmdb_movie_keyword.keyword_id ORDER BY tmdb_movie_keyword."order" ASC) as ids
            FROM
              tmdb_movie_keyword
            WHERE
              tmdb_movie_keyword.movie_id = tmdb_movie.id
          ) keyword ON true
        WHERE
          id IN (${sql.raw(tmdbIds.map((id) => `${id}`).join(", "))})
      `);
      const movies: Movie[] = rows.map((row) => ({
        id: String(row.id),
        genreIds: row.genres_ids?.map((genreId) => String(genreId)) ?? [],
        keywordIds: row.keyword_ids?.map((genreId) => String(genreId)) ?? [],
        images:
          row.images_urls &&
          row.images_idxs &&
          row.images_widths &&
          row.images_heights &&
          row.images_types
            ? groupBy(
                zip5(
                  row.images_urls,
                  row.images_idxs,
                  row.images_widths,
                  row.images_heights,
                  row.images_types,
                ).map(([url, index, width, height, type]) => ({
                  height,
                  index,
                  url,
                  width,
                  type,
                })),
                (item) => String(item.index),
              ).map((group) => ({
                sizes: group.items.map((item) => ({
                  height: item.height,
                  width: item.width,
                  url: item.url,
                })),
                type: group.items[0].type as "backdrop" | "poster",
              }))
            : [],
      }));
      const hash = new Map(movies.map((m) => [m.id, m]));

      return tmdbIds.map((id) => hash.get(id) ?? null);
    });
  }

  async findAll(): Promise<Movie[]> {
    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{
        id: number;
        images_idxs: number[] | null;
        images_urls: string[] | null;
        images_widths: number[] | null;
        images_heights: number[] | null;
        images_types: string[] | null;
        genres_ids: number[] | null;
        keyword_ids: number[] | null;
      }>(sql`
        SELECT
          id,
          images.idxs as images_idxs,
          images.urls as images_urls,
          images.widths as images_widths,
          images.heights as images_heights,
          images.types as images_types,
          genres.ids as genres_ids,
          keyword.ids as keyword_ids
        FROM
          tmdb_movie
        LEFT JOIN LATERAL
          (
            SELECT
              ARRAY_AGG(tmdb_movie_image.order ORDER BY "order" ASC, width ASC, url DESC) as idxs,
              ARRAY_AGG(tmdb_movie_image.url ORDER BY "order" ASC, width ASC, url DESC) as urls,
              ARRAY_AGG(tmdb_movie_image.width ORDER BY "order" ASC, width ASC, url DESC) as widths,
              ARRAY_AGG(tmdb_movie_image.height ORDER BY "order" ASC, width ASC, url DESC) as heights,
              ARRAY_AGG(tmdb_movie_image.type ORDER BY "order" ASC, width ASC, url DESC) as types
            FROM
              tmdb_movie_image
            WHERE
              tmdb_movie_image.movie_id = tmdb_movie.id
          ) images ON true
        LEFT JOIN LATERAL
          (
            SELECT
              ARRAY_AGG(tmdb_movie_genre.genre_id ORDER BY tmdb_movie_genre."order" ASC) as ids
            FROM
              tmdb_movie_genre
            WHERE
              tmdb_movie_genre.movie_id = tmdb_movie.id
          ) genres ON true
        LEFT JOIN LATERAL
          (
            SELECT
              ARRAY_AGG(tmdb_movie_keyword.keyword_id ORDER BY tmdb_movie_keyword."order" ASC) as ids
            FROM
              tmdb_movie_keyword
            WHERE
              tmdb_movie_keyword.movie_id = tmdb_movie.id
          ) keyword ON true
        ORDER BY
          tmdb_movie.updated_at DESC
      `);

      return rows.map((row) => ({
        id: String(row.id),
        genreIds: row.genres_ids?.map((genreId) => String(genreId)) ?? [],
        keywordIds: row.keyword_ids?.map((genreId) => String(genreId)) ?? [],
        images:
          row.images_urls &&
          row.images_idxs &&
          row.images_widths &&
          row.images_heights &&
          row.images_types
            ? groupBy(
                zip5(
                  row.images_urls,
                  row.images_idxs,
                  row.images_widths,
                  row.images_heights,
                  row.images_types,
                ).map(([url, index, width, height, type]) => ({
                  height,
                  index,
                  url,
                  width,
                  type,
                })),
                (item) => String(item.index),
              ).map((group) => ({
                sizes: group.items.map((item) => ({
                  height: item.height,
                  width: item.width,
                  url: item.url,
                })),
                type: group.items[0].type as "backdrop" | "poster",
              }))
            : [],
      }));
    });
  }

  async setMany(movies: Movie[]): Promise<void> {
    return this.pool.transaction(async (connection) => {
      await connection.query(
        sql`CREATE TEMP TABLE tmp_tmdb_movie (LIKE tmdb_movie)`,
      );
      await connection.copyFrom(
        "COPY tmp_tmdb_movie (id, created_at, updated_at) FROM STDIN",
        movies.map((movie) => ({
          id: Number(movie.id),
          created_at: new Date(),
          updated_at: new Date(),
        })),
      );
      await connection.query(
        sql`
          INSERT INTO tmdb_movie
          (SELECT * FROM tmp_tmdb_movie)
          ON CONFLICT (id) DO NOTHING
        `,
      );

      await connection.query(
        sql`CREATE TEMP TABLE tmp_tmdb_movie_genre (LIKE tmdb_movie_genre)`,
      );
      await connection.copyFrom(
        'COPY tmp_tmdb_movie_genre (movie_id, genre_id, "order", created_at, updated_at) FROM STDIN',
        movies.flatMap((movie) =>
          movie.genreIds.map((genreId, index) => ({
            movie_id: Number(movie.id),
            genre_id: Number(genreId),
            order: index,
            created_at: new Date(),
            updated_at: new Date(),
          })),
        ),
      );
      await connection.query(
        sql`
          INSERT INTO tmdb_movie_genre
          (SELECT * FROM tmp_tmdb_movie_genre)
          ON CONFLICT (movie_id, genre_id)
            DO UPDATE SET "order" = excluded."order",
                          updated_at = excluded.updated_at
            WHERE (tmdb_movie_genre."order") IS DISTINCT FROM (excluded."order")
        `,
      );
      await connection.query(
        sql`
          DELETE FROM
            tmdb_movie_genre
          WHERE
            EXISTS (
              SELECT
              FROM
                tmp_tmdb_movie
              WHERE
                tmp_tmdb_movie.id = tmdb_movie_genre.movie_id
            )
            AND NOT EXISTS (
              SELECT
              FROM
                tmp_tmdb_movie_genre
              WHERE
                tmp_tmdb_movie_genre.movie_id = tmdb_movie_genre.movie_id
                AND tmp_tmdb_movie_genre.genre_id = tmdb_movie_genre.genre_id
            )
        `,
      );

      await connection.query(
        sql`CREATE TEMP TABLE tmp_tmdb_movie_keyword (LIKE tmdb_movie_keyword)`,
      );
      await connection.copyFrom(
        'COPY tmp_tmdb_movie_keyword (movie_id, keyword_id, "order", created_at, updated_at) FROM STDIN',
        movies.flatMap((movie) =>
          movie.keywordIds.map((keywordId, index) => ({
            movie_id: Number(movie.id),
            keyword_id: Number(keywordId),
            order: index,
            created_at: new Date(),
            updated_at: new Date(),
          })),
        ),
      );
      await connection.query(
        sql`
          INSERT INTO tmdb_movie_keyword
          (SELECT * FROM tmp_tmdb_movie_keyword)
          ON CONFLICT (movie_id, keyword_id)
            DO UPDATE SET "order" = excluded."order",
                          updated_at = excluded.updated_at
            WHERE (tmdb_movie_keyword."order") IS DISTINCT FROM (excluded."order")
        `,
      );
      await connection.query(
        sql`
          DELETE FROM
            tmdb_movie_keyword
          WHERE
            EXISTS (
              SELECT
              FROM
                tmp_tmdb_movie
              WHERE
                tmp_tmdb_movie.id = tmdb_movie_keyword.movie_id
            )
            AND NOT EXISTS (
              SELECT
              FROM
                tmp_tmdb_movie_keyword
              WHERE
                tmp_tmdb_movie_keyword.movie_id = tmdb_movie_keyword.movie_id
                AND tmp_tmdb_movie_keyword.keyword_id = tmdb_movie_keyword.keyword_id
            )
        `,
      );

      await connection.query(
        sql`CREATE TEMP TABLE tmp_tmdb_movie_image (LIKE tmdb_movie_image)`,
      );
      await connection.copyFrom(
        `COPY tmp_tmdb_movie_image (movie_id, "order", "url", height, width, type, created_at, updated_at) FROM STDIN`,
        movies
          .map((movie) =>
            movie.images
              .map((image, index) =>
                image.sizes.map((size) => ({
                  movie_id: Number(movie.id),
                  order: index,
                  url: size.url,
                  height: size.height,
                  width: size.width,
                  type: image.type,
                  created_at: new Date(),
                  updated_at: new Date(),
                })),
              )
              .reduce((acc, x) => [...acc, ...x], []),
          )
          .reduce((acc, x) => [...acc, ...x], []),
      );
      await connection.query(
        sql`
          INSERT INTO tmdb_movie_image
          (SELECT * FROM tmp_tmdb_movie_image)
          ON CONFLICT (movie_id, "order", "url")
            DO UPDATE SET height = excluded.height,
                          width = excluded.width,
                          type = excluded.type,
                          updated_at = excluded.updated_at
            WHERE (tmdb_movie_image.height, tmdb_movie_image.width, tmdb_movie_image.type) IS DISTINCT FROM (excluded.height, excluded.width, excluded.type)
        `,
      );
      await connection.query(
        sql`
          DELETE FROM
            tmdb_movie_image
          WHERE
            EXISTS (
              SELECT
              FROM
                tmp_tmdb_movie
              WHERE
                tmp_tmdb_movie.id = tmdb_movie_image.movie_id
            )
            AND NOT EXISTS (
              SELECT
              FROM
                tmp_tmdb_movie_image
              WHERE
                tmp_tmdb_movie_image.movie_id = tmdb_movie_image.movie_id
                AND tmp_tmdb_movie_image."order" = tmdb_movie_image."order"
                AND tmp_tmdb_movie_image.url = tmdb_movie_image.url
            )
        `,
      );

      await connection.query(sql`DROP TABLE tmp_tmdb_movie`);
      await connection.query(sql`DROP TABLE tmp_tmdb_movie_genre`);
      await connection.query(sql`DROP TABLE tmp_tmdb_movie_keyword`);
      await connection.query(sql`DROP TABLE tmp_tmdb_movie_image`);
    });
  }
}

function zip5<A, B, C, D, E>(
  as: A[],
  bs: B[],
  cs: C[],
  ds: D[],
  es: E[],
): [A, B, C, D, E][] {
  const result: [A, B, C, D, E][] = [];
  const length = Math.min(
    as.length,
    bs.length,
    cs.length,
    ds.length,
    es.length,
  );

  for (let i = 0; i < length; i += 1) {
    result.push([as[i], bs[i], cs[i], ds[i], es[i]]);
  }

  return result;
}

function groupBy<T, K>(
  items: T[],
  fn: (item: T) => K,
): { key: K; items: T[] }[] {
  const groups: { key: K; items: T[] }[] = [];

  items.forEach((item) => {
    const key = fn(item);
    const group = groups.find((g) => g.key === key);

    if (group) {
      group.items.push(item);
    } else {
      groups.push({
        key,
        items: [item],
      });
    }
  });

  return groups;
}
