import { Movie, MovieRepository } from "../jobs/PullKinopoiskMarksJob.js";
import ConnectionPool from "./ConnectionPool.js";
import sql from "./sql.js";

export default class PostgresKinopoiskMovieRepository
  implements MovieRepository
{
  constructor(private pool: ConnectionPool) {}

  async findMany(kinopoiskIds: string[]): Promise<(Movie | null)[]> {
    if (kinopoiskIds.length === 0) {
      return [];
    }

    return this.pool.transaction(async (connection) => {
      const rawMovies = await connection.query<{
        id: number;
        title: string;
        year: number | null;
        created_at: Date;
        updated_at: Date;
      }>(
        sql`
          SELECT
            id,
            title,
            year,
            created_at,
            updated_at
          FROM
            kinopoisk_movie
          WHERE
            id IN (${sql.raw(kinopoiskIds.map((id) => `${id}`).join(", "))})
        `,
      );
      const movies: Movie[] = rawMovies.map((m) => ({
        id: String(m.id),
        title: m.title,
        year: m.year,
        createdAt: m.created_at,
        updatedAt: m.updated_at,
      }));
      const hash = new Map(movies.map((m) => [m.id, m]));

      return kinopoiskIds.map((url) => hash.get(url) ?? null);
    });
  }

  async setMany(movies: Movie[]): Promise<void> {
    return this.pool.transaction(async (connection) => {
      await connection.query(
        sql`CREATE TEMP TABLE tmp_movie (LIKE kinopoisk_movie)`,
      );
      await connection.copyFrom(
        "COPY tmp_movie (id, title, year, created_at, updated_at) FROM STDIN",
        movies.map((movie) => ({
          id: Number(movie.id),
          title: movie.title,
          year: movie.year,
          created_at: movie.createdAt,
          updated_at: movie.updatedAt,
        })),
      );
      const insertedCount = await connection.mutate(
        sql`
          INSERT INTO kinopoisk_movie
          (SELECT * FROM tmp_movie)
          ON CONFLICT (id)
            DO UPDATE SET title = excluded.title,
                          year = excluded.year,
                          updated_at = excluded.updated_at
            WHERE (kinopoisk_movie.title, kinopoisk_movie.year) IS DISTINCT FROM (excluded.title, excluded.year)
        `,
      );

      await connection.query(sql`DROP TABLE tmp_movie`);

      if (insertedCount > 0) {
        await connection.query(
          sql`REFRESH MATERIALIZED VIEW CONCURRENTLY "graphql_movie"`,
        );
      }
    });
  }
}
