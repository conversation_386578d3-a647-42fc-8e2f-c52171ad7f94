import { Query<PERSON><PERSON><PERSON> } from "../jobs/PullTmdbContentJob.js";
import ConnectionPool from "./ConnectionPool.js";
import sql from "./sql.js";

export default class PostgresTmdbQueryHandler implements QueryHandler {
  constructor(private pool: ConnectionPool) {}

  async getNewMovieIds(): Promise<string[]> {
    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{
        tmdb_id: number;
      }>(sql`
        SELECT
          tmdb_id
        FROM
          wikidata_movie
        WHERE
          tmdb_id IS NOT NULL
      `);

      return rows.map((row) => String(row.tmdb_id));
    });
  }

  async getNewPersonIds(): Promise<string[]> {
    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{
        tmdb_id: number;
      }>(sql`
        SELECT
          tmdb_id
        FROM
          wikidata_person
        WHERE
          tmdb_id IS NOT NULL
      `);

      return rows.map((row) => String(row.tmdb_id));
    });
  }

  async getRandomMovieIds(limit: number): Promise<string[]> {
    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{ id: number }>(sql`
        SELECT id
        FROM tmdb_movie
        ORDER BY RANDOM()
        LIMIT ${limit}
      `);
      return rows.map((row) => String(row.id));
    });
  }

  async getRandomPersonIds(limit: number): Promise<string[]> {
    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{ id: number }>(sql`
        SELECT id
        FROM tmdb_person
        ORDER BY RANDOM()
        LIMIT ${limit}
      `);
      return rows.map((row) => String(row.id));
    });
  }
}
