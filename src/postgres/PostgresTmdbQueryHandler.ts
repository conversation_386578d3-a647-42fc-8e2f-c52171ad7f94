import { ImageToDownload, QueryHand<PERSON> } from "../jobs/PullTmdbContentJob.js";
import ConnectionPool from "./ConnectionPool.js";
import sql from "./sql.js";

export default class PostgresTmdbQueryHandler implements QueryHandler {
  constructor(private pool: ConnectionPool) {}

  async getNewMovieIds(): Promise<string[]> {
    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{
        tmdb_id: number;
      }>(sql`
        SELECT
          wikidata_movie.tmdb_id
        FROM
          wikidata_movie
        LEFT JOIN
          tmdb_movie ON tmdb_movie.id = wikidata_movie.tmdb_id
        WHERE
          wikidata_movie.tmdb_id IS NOT NULL
          AND tmdb_movie.id IS NULL
      `);

      return rows.map((row) => String(row.tmdb_id));
    });
  }

  async getNewPersonIds(): Promise<string[]> {
    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{
        tmdb_id: number;
      }>(sql`
        SELECT
          wikidata_person.tmdb_id
        FROM
          wikidata_person
        LEFT JOIN
          tmdb_person ON tmdb_person.id = wikidata_person.tmdb_id
        WHERE
          wikidata_person.tmdb_id IS NOT NULL
          AND tmdb_person.id IS NULL
      `);

      return rows.map((row) => String(row.tmdb_id));
    });
  }

  async getRandomMovieIds(limit: number): Promise<string[]> {
    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{ id: number }>(sql`
        SELECT id
        FROM tmdb_movie
        ORDER BY RANDOM()
        LIMIT ${limit}
      `);
      return rows.map((row) => String(row.id));
    });
  }

  async getRandomPersonIds(limit: number): Promise<string[]> {
    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{ id: number }>(sql`
        SELECT id
        FROM tmdb_person
        ORDER BY RANDOM()
        LIMIT ${limit}
      `);
      return rows.map((row) => String(row.id));
    });
  }

  async getImagesToDownload(): Promise<ImageToDownload[]> {
    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{
        url: string;
        width: number;
        height: number;
      }>(sql`
        WITH movie_images AS (
          -- Get best backdrop and poster for each movie
          SELECT DISTINCT ON (movie_id, type)
            movie_id,
            url,
            width,
            height,
            type,
            ABS(width - 1280) as width_diff
          FROM tmdb_movie_image
          WHERE type IN ('backdrop', 'poster')
          ORDER BY movie_id, type, ABS(width - 1280) ASC, width ASC, url DESC
        ),
        person_images AS (
          -- Get best image for each person (closest to 180px width)
          SELECT DISTINCT ON (person_id)
            person_id,
            url,
            width,
            height,
            ABS(width - 180) as width_diff
          FROM tmdb_person_image
          ORDER BY person_id, ABS(width - 180) ASC, width ASC, url DESC
        ),
        all_candidate_images AS (
          SELECT url, width, height FROM movie_images
          UNION ALL
          SELECT url, width, height FROM person_images
        )
        SELECT
          aci.url,
          aci.width,
          aci.height
        FROM all_candidate_images aci
        LEFT JOIN image i ON i.source_url = aci.url
        WHERE i.source_url IS NULL
      `);

      return rows.map((row) => ({
        url: row.url,
        width: row.width,
        height: row.height,
      }));
    });
  }
}
