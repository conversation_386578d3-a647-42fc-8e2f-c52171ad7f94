import { ComposeLoglineJob, JobQueue } from "../jobs/ComposeLoglinesJob.js";
import ConnectionPool from "./ConnectionPool.js";
import sql from "./sql.js";

export default class PostgresLoglineJobQueue implements JobQueue {
  constructor(private pool: ConnectionPool) {}

  async getJobsQueue(): Promise<ComposeLoglineJob[]> {
    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{
        lang: "en" | "ru";
        slug: string;
        plot: string;
      }>(
        sql`
          SELECT DISTINCT ON (best_position)
            wikipedia_article.lang as "lang",
            wikipedia_article.slug as "slug",
            wikipedia_article.plot as "plot",
            (
              SELECT
                MIN(graphql_movie_top."position")
              FROM
                graphql_movie_top
              WHERE
                graphql_movie_top.movie_id = wikidata_movie.kinopoisk_id
            ) as "best_position"
          FROM
            wikidata_movie
          JOIN
            wikipedia_article
            ON (
              wikipedia_article.lang = 'ru'
              AND wikipedia_article.slug = wikidata_movie.wikipedia_ru_slug
            ) OR (
              wikipedia_article.lang = 'en'
              AND wikipedia_article.slug = wikidata_movie.wikipedia_en_slug
            )
          WHERE
            wikipedia_article.plot IS NOT NULL
            AND EXISTS (
              SELECT
              FROM
                graphql_movie_top
              WHERE
                graphql_movie_top.movie_id = wikidata_movie.kinopoisk_id
            )
            AND NOT EXISTS (
              SELECT
              FROM
                llm_logline
              WHERE
                llm_logline.model IN ('deepseek-chat', 'deepseek-reasoner')
                AND (
                  (
                    llm_logline.wikipedia_lang = 'ru'
                    AND llm_logline.wikipedia_slug = wikidata_movie.wikipedia_ru_slug
                  ) OR (
                      llm_logline.wikipedia_lang = 'en'
                      AND llm_logline.wikipedia_slug = wikidata_movie.wikipedia_en_slug
                  )
                )
            )
          ORDER BY
            best_position ASC NULLS LAST,
            LENGTH(plot) DESC
          LIMIT
            100
        `,
      );

      return rows.map(
        (row): ComposeLoglineJob => ({
          wikipedia: {
            lang: row.lang,
            slug: row.slug,
            plot: row.plot,
          },
        }),
      );
    });
  }
}
