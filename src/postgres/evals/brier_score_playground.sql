WITH user_mark_mean_absolute_error AS (
  SELECT
    kinopoisk_movie_mark.user_id as user_id,
    another_kinopoisk_movie_mark.user_id as friend_id,
    SUM(
      ABS(
        (
          CASE
            WHEN kinopoisk_movie_mark.mark >= 9 THEN 1.0
            ELSE 0
          END
        )
        - (
          CASE
            WHEN another_kinopoisk_movie_mark.mark >= 9 THEN 1.0
            ELSE 0
          END
        ) + 0.0
      )
    ) / COUNT(*) as mae
  FROM
    account
  JOIN
    kinopoisk_movie_mark
    ON kinopoisk_movie_mark.user_id = account.kinopoisk_id
  JOIN
    kinopoisk_movie_mark another_kinopoisk_movie_mark
    ON another_kinopoisk_movie_mark.movie_id = kinopoisk_movie_mark.movie_id
    AND another_kinopoisk_movie_mark.user_id != kinopoisk_movie_mark.user_id
  WHERE
    kinopoisk_movie_mark.mark >= 9
    OR another_kinopoisk_movie_mark.mark >= 9
  GROUP BY
    kinopoisk_movie_mark.user_id,
    another_kinopoisk_movie_mark.user_id
  HAVING
    COUNT(*) >= 50
), ordered_friend AS (
  SELECT
    user_mark_mean_absolute_error.user_id as user_id,
    user_mark_mean_absolute_error.friend_id,
    ROW_NUMBER() OVER (
      PARTITION BY
        user_mark_mean_absolute_error.user_id
      ORDER BY
        user_mark_mean_absolute_error.mae ASC NULLS LAST,
        user_mark_mean_absolute_error.friend_id ASC
    ) as "order"
  FROM
    user_mark_mean_absolute_error
), graphql_friend AS (
  SELECT
    ordered_friend.user_id as user_id,
    ordered_friend.friend_id as friend_id,
    ordered_friend."order" as "order"
  FROM
    ordered_friend
  WHERE
    ordered_friend."order" <= 200
), kinopoisk_movie_mark_categorized AS (
  SELECT
    kinopoisk_movie_mark.user_id as user_id,
    kinopoisk_movie_mark.movie_id as movie_id,
    kinopoisk_movie_mark.mark BETWEEN 1 AND 6 as bad_mark,
    kinopoisk_movie_mark.mark BETWEEN 7 AND 10 as good_mark,
    kinopoisk_movie_mark.mark BETWEEN 9 AND 10 as best_mark
  FROM
    kinopoisk_movie_mark
), kinopoisk_movie_mark_aggregated AS (
  SELECT
    account.kinopoisk_id as user_id,
    kinopoisk_movie_mark_categorized.movie_id as movie_id,
    COUNT(NULLIF(kinopoisk_movie_mark_categorized.bad_mark, false)) as bad_marks,
    COUNT(NULLIF(kinopoisk_movie_mark_categorized.good_mark, false)) as good_marks,
    COUNT(NULLIF(kinopoisk_movie_mark_categorized.best_mark, false)) as best_marks
  FROM
    account
  JOIN
    graphql_friend
    ON graphql_friend.user_id = account.kinopoisk_id
  JOIN
    kinopoisk_movie_mark_categorized
    ON kinopoisk_movie_mark_categorized.user_id = graphql_friend.friend_id
  GROUP BY
    account.kinopoisk_id, kinopoisk_movie_mark_categorized.movie_id
), graphql_movie_top AS (
  SELECT
    account.kinopoisk_id as user_id,
    kinopoisk_movie_mark_aggregated.movie_id as movie_id,
    (ROW_NUMBER() OVER (
      PARTITION BY kinopoisk_movie_mark_aggregated.user_id
      ORDER BY
        CASE
          WHEN ROUND(100 * wilson_score_lower_bound(
            kinopoisk_movie_mark_aggregated.best_marks,
            kinopoisk_movie_mark_aggregated.good_marks - kinopoisk_movie_mark_aggregated.best_marks + kinopoisk_movie_mark_aggregated.bad_marks,
            1.96
          )) < 5 OR ROUND(100 * wilson_score_lower_bound(
	        kinopoisk_movie_mark_aggregated.good_marks,
	        kinopoisk_movie_mark_aggregated.bad_marks,
	        1.645
	      )) < 50
          THEN 0
          ELSE ABS(ROUND(100 * wilson_score_lower_bound(
            kinopoisk_movie_mark_aggregated.best_marks,
            kinopoisk_movie_mark_aggregated.good_marks - kinopoisk_movie_mark_aggregated.best_marks + kinopoisk_movie_mark_aggregated.bad_marks,
            1.96
          )))
        END DESC,
        ROUND(100 * wilson_score_lower_bound(
          kinopoisk_movie_mark_aggregated.good_marks,
          kinopoisk_movie_mark_aggregated.bad_marks,
          1.645
        )) DESC
    ))::integer as "position",
    CASE
      WHEN ABS(ROUND(100 * wilson_score_lower_bound(
        kinopoisk_movie_mark_aggregated.best_marks,
        kinopoisk_movie_mark_aggregated.good_marks - kinopoisk_movie_mark_aggregated.best_marks + kinopoisk_movie_mark_aggregated.bad_marks,
        1.96
      ))) < 5 OR ROUND(100 * wilson_score_lower_bound(
        kinopoisk_movie_mark_aggregated.good_marks,
        kinopoisk_movie_mark_aggregated.bad_marks,
        1.645
      )) < 50
      THEN 0
      ELSE ABS(ROUND(100 * wilson_score_lower_bound(
        kinopoisk_movie_mark_aggregated.best_marks,
        kinopoisk_movie_mark_aggregated.good_marks - kinopoisk_movie_mark_aggregated.best_marks + kinopoisk_movie_mark_aggregated.bad_marks,
        1.96
      )))
    END as best_marks_percentage,
    ABS(ROUND(100 * wilson_score_lower_bound(
      kinopoisk_movie_mark_aggregated.good_marks,
      kinopoisk_movie_mark_aggregated.bad_marks,
      1.645
    ))) as good_marks_percentage
  FROM
    account
  JOIN
    kinopoisk_movie_mark_aggregated
    ON kinopoisk_movie_mark_aggregated.user_id = account.id
), dataset AS (
  SELECT
    (100 - graphql_movie_top.good_marks_percentage) / 100.0 as f1,
    (graphql_movie_top.good_marks_percentage / 100.0) * ((100 - graphql_movie_top.best_marks_percentage) / 100.0) as f2,
    (graphql_movie_top.good_marks_percentage / 100.0) * (graphql_movie_top.best_marks_percentage / 100.0) as f3,
    CASE WHEN graphql_user_movie_mark.mark < 7 THEN 1 ELSE 0 END as o1,
    CASE WHEN graphql_user_movie_mark.mark >= 7 AND graphql_user_movie_mark.mark < 9 THEN 1 ELSE 0 END as o2,
    CASE WHEN graphql_user_movie_mark.mark >= 9 THEN 1 ELSE 0 END as o3
  FROM
    graphql_user_movie_mark
  JOIN
    graphql_movie_top
    ON graphql_movie_top.movie_id = graphql_user_movie_mark.movie_id
    AND graphql_movie_top.user_id = graphql_user_movie_mark.user_id 
  WHERE
    graphql_user_movie_mark.user_id = 789114
)

SELECT
	1 -
	(1.0 * SUM(POW(f1 - o1, 2) + POW(f2 - o2, 2) + POW(f3 - o3, 2)) / COUNT(*) / 2.0) / 
	(
		(
			1.0 * SUM(POW((SELECT 1.0 * COUNT(*) FILTER (WHERE dataset.o1 = 1) / COUNT(*) FROM dataset) - o1, 2) +
			POW((SELECT 1.0 * COUNT(*) FILTER (WHERE dataset.o2 = 1) / COUNT(*) FROM dataset) - o2, 2) +
			POW((SELECT 1.0 * COUNT(*) FILTER (WHERE dataset.o3 = 1) / COUNT(*) FROM dataset) - o3, 2))
		) / 
		COUNT(*) / 2.0
	) as brier_skill_score,
	COUNT(*) as dataset_size
FROM
	dataset