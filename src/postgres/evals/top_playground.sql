WITH user_mark_mean_absolute_error AS (
  SELECT
    kinopoisk_movie_mark.user_id as user_id,
    another_kinopoisk_movie_mark.user_id as friend_id,
    SUM(
      ABS(
        (
          CASE
            WHEN kinopoisk_movie_mark.mark >= 9 THEN 1.0
            ELSE 0
          END
        )
        - (
          CASE
            WHEN another_kinopoisk_movie_mark.mark >= 9 THEN 1.0
            ELSE 0
          END
        ) + 0.0
      )
    ) / COUNT(*) as mae
  FROM
    account
  JOIN
    kinopoisk_movie_mark
    ON kinopoisk_movie_mark.user_id = account.kinopoisk_id
  JOIN
    kinopoisk_movie_mark another_kinopoisk_movie_mark
    ON another_kinopoisk_movie_mark.movie_id = kinopoisk_movie_mark.movie_id
    AND another_kinopoisk_movie_mark.user_id != kinopoisk_movie_mark.user_id
  WHERE
    kinopoisk_movie_mark.mark >= 9
    OR another_kinopoisk_movie_mark.mark >= 9
  GROUP BY
    kinopoisk_movie_mark.user_id,
    another_kinopoisk_movie_mark.user_id
  HAVING
    COUNT(*) >= 50
), ordered_friend AS (
  SELECT
    user_mark_mean_absolute_error.user_id as user_id,
    user_mark_mean_absolute_error.friend_id,
    ROW_NUMBER() OVER (
      PARTITION BY
        user_mark_mean_absolute_error.user_id
      ORDER BY
        user_mark_mean_absolute_error.mae ASC NULLS LAST,
        user_mark_mean_absolute_error.friend_id ASC
    ) as "order"
  FROM
    user_mark_mean_absolute_error
), graphql_friend AS (
  SELECT
    ordered_friend.user_id as user_id,
    ordered_friend.friend_id as friend_id,
    ordered_friend."order" as "order"
  FROM
    ordered_friend
  WHERE
    ordered_friend."order" <= 200
), kinopoisk_movie_mark_categorized AS (
  SELECT
    kinopoisk_movie_mark.user_id as user_id,
    kinopoisk_movie_mark.movie_id as movie_id,
    kinopoisk_movie_mark.mark BETWEEN 1 AND 6 as bad_mark,
    kinopoisk_movie_mark.mark BETWEEN 7 AND 10 as good_mark,
    kinopoisk_movie_mark.mark BETWEEN 9 AND 10 as best_mark
  FROM
    kinopoisk_movie_mark
), kinopoisk_movie_mark_aggregated AS (
  SELECT
    account.kinopoisk_id as user_id,
    kinopoisk_movie_mark_categorized.movie_id as movie_id,
    COUNT(NULLIF(kinopoisk_movie_mark_categorized.bad_mark, false)) as bad_marks,
    COUNT(NULLIF(kinopoisk_movie_mark_categorized.good_mark, false)) as good_marks,
    COUNT(NULLIF(kinopoisk_movie_mark_categorized.best_mark, false)) as best_marks
  FROM
    account
  JOIN
    graphql_friend
    ON graphql_friend.user_id = account.kinopoisk_id
  JOIN
    kinopoisk_movie_mark_categorized
    ON kinopoisk_movie_mark_categorized.user_id = graphql_friend.friend_id
  GROUP BY
    account.kinopoisk_id, kinopoisk_movie_mark_categorized.movie_id
), graphql_movie_top AS (
  SELECT
    account.kinopoisk_id as user_id,
    kinopoisk_movie_mark_aggregated.movie_id as movie_id,
    (ROW_NUMBER() OVER (
      PARTITION BY kinopoisk_movie_mark_aggregated.user_id
      ORDER BY
        CASE
          WHEN ROUND(100 * wilson_score_lower_bound(
            kinopoisk_movie_mark_aggregated.best_marks,
            kinopoisk_movie_mark_aggregated.good_marks - kinopoisk_movie_mark_aggregated.best_marks + kinopoisk_movie_mark_aggregated.bad_marks,
            1.96
          )) < 5 OR ROUND(100 * wilson_score_lower_bound(
	        kinopoisk_movie_mark_aggregated.good_marks,
	        kinopoisk_movie_mark_aggregated.bad_marks,
	        1.645
	      )) < 50
          THEN 0
          ELSE ABS(ROUND(100 * wilson_score_lower_bound(
            kinopoisk_movie_mark_aggregated.best_marks,
            kinopoisk_movie_mark_aggregated.good_marks - kinopoisk_movie_mark_aggregated.best_marks + kinopoisk_movie_mark_aggregated.bad_marks,
            1.96
          )))
        END DESC,
        ROUND(100 * wilson_score_lower_bound(
          kinopoisk_movie_mark_aggregated.good_marks,
          kinopoisk_movie_mark_aggregated.bad_marks,
          1.645
        )) DESC
    ))::integer as "position",
    CASE
      WHEN ABS(ROUND(100 * wilson_score_lower_bound(
        kinopoisk_movie_mark_aggregated.best_marks,
        kinopoisk_movie_mark_aggregated.good_marks - kinopoisk_movie_mark_aggregated.best_marks + kinopoisk_movie_mark_aggregated.bad_marks,
        1.96
      ))) < 5 OR ROUND(100 * wilson_score_lower_bound(
        kinopoisk_movie_mark_aggregated.good_marks,
        kinopoisk_movie_mark_aggregated.bad_marks,
        1.645
      )) < 50
      THEN 0
      ELSE ABS(ROUND(100 * wilson_score_lower_bound(
        kinopoisk_movie_mark_aggregated.best_marks,
        kinopoisk_movie_mark_aggregated.good_marks - kinopoisk_movie_mark_aggregated.best_marks + kinopoisk_movie_mark_aggregated.bad_marks,
        1.96
      )))
    END as best_marks_percentage,
    ABS(ROUND(100 * wilson_score_lower_bound(
      kinopoisk_movie_mark_aggregated.good_marks,
      kinopoisk_movie_mark_aggregated.bad_marks,
      1.645
    ))) as good_marks_percentage
  FROM
    account
  JOIN
    kinopoisk_movie_mark_aggregated
    ON kinopoisk_movie_mark_aggregated.user_id = account.id
)
	
SELECT
	-- graphql_movie_top."position",
	graphql_movie.title || ' (' || graphql_movie.year || ')'
	-- graphql_movie_top.good_marks_percentage,
	-- graphql_movie_top.best_marks_percentage
FROM
	graphql_movie_top
JOIN
	graphql_movie
	ON graphql_movie.id = graphql_movie_top.movie_id
WHERE
	graphql_movie_top.user_id = 789114
	AND graphql_movie.year >= 1960
	AND graphql_movie.year < 1970
ORDER BY
	graphql_movie_top."position" ASC
	-- (SELECT kinopoisk_movie_mark."order" FROM kinopoisk_movie_mark WHERE kinopoisk_movie_mark.user_id = graphql_movie_top.user_id AND kinopoisk_movie_mark.movie_id = graphql_movie.id) DESC NULLS LAST
LIMIT
	100