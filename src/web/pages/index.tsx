import * as React from "react";

import Layout from "../_layout.js";
import { useAuthentication } from "../components/Authentication.js";
import Head from "../components/Head.js";
import MovieLargeSnippet from "../components/MovieLargeSnippet.js";
import MovieSmallSnippet from "../components/MovieSmallSnippet.js";
import { useRouter } from "../components/Router.js";
import SegmentedControl from "../components/SegmentedControl.js";
import { graphql, useQuery } from "../graphql/client.js";

const GetFeed = graphql(/* GraphQL */ `
  query GetFeed {
    feed {
      ... on UserMark {
        date
        mark
        movie {
          id
          ...MovieLargeSnippet_MovieFragment
        }
        user {
          id
          ...MovieLargeSnippet_UserFragment
        }
      }
    }
    genres {
      id
      label
      slug
    }
    movies(sort: RANDOM, viewed: false, limit: 5) {
      id
      ...MovieSmallSnippet_MovieFragment
    }
  }
`);

export const FeedPage: React.FC = () => {
  const authentication = useAuthentication();
  const router = useRouter();
  const { data } = useQuery(GetFeed);

  return (
    <main className="container mx-auto px-5 antialiased">
      <Head>
        <title>
          {authentication.state === "unauthenticated"
            ? "Зырь. Находит 💎 кино по твоим интересам"
            : "Зырь"}
        </title>
      </Head>

      <div className="mt-6 text-xl">
        Жанр:
        <SegmentedControl
          className="ml-1.5"
          items={[
            ...(data?.genres ?? []).map((g) => ({
              label: g.label,
              selected: false,
              href: router.stringify("/movies/genre/:genre", {
                genre: g.slug,
              }),
            })),
          ]}
        />
      </div>

      <h2 className="mt-5 text-3xl font-bold tracking-tight">
        Случайные фильмы
      </h2>
      <div className="mt-2 mb-5 grid grid-cols-5 gap-4">
        {data?.movies.map((movie) => (
          <MovieSmallSnippet key={movie.id} movie={movie} />
        ))}
      </div>

      <h2 className="mt-5 text-3xl font-bold tracking-tight">Друзья смотрят</h2>
      <div className="mt-2 mb-10 grid grid-cols-2 gap-4">
        {data?.feed.map(({ movie, date, mark, user }) => (
          <MovieLargeSnippet
            key={date + user.id + movie.id}
            movie={movie}
            aux={{ user, mark, type: "friend" }}
          />
        ))}
      </div>
    </main>
  );
};

const IndexPage: React.FC = () => {
  return (
    <Layout>
      <FeedPage />
    </Layout>
  );
};

export default IndexPage;
