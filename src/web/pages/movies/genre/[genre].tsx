import * as React from "react";

import Head from "../../../components/Head.js";
import MovieLargeSnippet from "../../../components/MovieLargeSnippet.js";
import { Rewrite, useRouter } from "../../../components/Router.js";
import SegmentedControl from "../../../components/SegmentedControl.js";
import { graphql, useQuery } from "../../../graphql/client.js";

const GetTopMovies = graphql(/* GraphQL */ `
  query GetTopMovies($genreSlug: String, $viewed: Boolean) {
    movies(genreSlug: $genreSlug, sort: RANDOM, viewed: $viewed, limit: 50) {
      id
      ...MovieLargeSnippet_MovieFragment
    }
  }
`);

const GetGenres = graphql(/* GraphQL */ `
  query GetGenres {
    genres {
      id
      label
      slug
    }
  }
`);

const MoviesPage: React.FC = () => {
  const router = useRouter();

  if (router.pattern !== "/movies/genre/:genre") {
    throw new Error("Unexpected pattern");
  }

  const genreSlug = router.params.genre;
  const viewed = router.url.searchParams.has("viewed");

  const { data } = useQuery(GetTopMovies, {
    genreSlug,
    viewed: viewed ? undefined : false,
  });
  const { data: genresData } = useQuery(
    GetGenres,
    {},
    {
      keepPreviousData: true,
    },
  );

  const setShowViewed = React.useCallback(
    (newShowViewed) => {
      const newUrl = new URL(router.url);

      if (newShowViewed) {
        newUrl.searchParams.set("viewed", "1");
      } else {
        newUrl.searchParams.delete("viewed");
      }

      router.push(newUrl.toString());
    },
    [router],
  );

  if (router.url.searchParams.get("sort") === "best_first") {
    return <Rewrite to={{ pathname: router.stringify("/list", {}) }} />;
  }

  return (
    <main className="container mx-auto px-5 antialiased">
      <Head>
        <title>Рекомендации – Зырь</title>
      </Head>
      <nav className="mt-6 text-xl">
        <div>
          Жанр:
          <SegmentedControl
            className="ml-1.5"
            items={[
              ...(genresData?.genres ?? []).map((g) => ({
                label: g.label,
                selected: genreSlug === g.slug,
                href:
                  genreSlug === g.slug
                    ? undefined
                    : router.stringify("/movies/genre/:genre", {
                        genre: g.slug,
                      }),
              })),
            ]}
          />
        </div>

        <div className="mt-4">
          <span className="relative inline-flex h-4 w-4 translate-y-[-1px] align-middle">
            {viewed ? (
              <svg
                className="inline-block"
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect
                  x="0.75"
                  y="0.75"
                  width="14.5"
                  height="14.5"
                  rx="3.25"
                  fill="black"
                  stroke="black"
                  strokeWidth="1.5"
                />
                <path
                  d="M3.5 9L7.5 12.5L13 3.5"
                  stroke="white"
                  strokeWidth="1.5"
                />
              </svg>
            ) : (
              <svg
                className="inline-block"
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect
                  x="0.75"
                  y="0.75"
                  width="14.5"
                  height="14.5"
                  rx="3.25"
                  stroke="black"
                  strokeWidth="1.5"
                />
              </svg>
            )}
            <input
              id="show-viewed"
              checked={viewed}
              onChange={(event) => setShowViewed(event.target.checked)}
              type="checkbox"
              className="absolute inset-0 h-4 w-4 cursor-pointer opacity-0"
            />
          </span>

          {/* eslint-disable-next-line jsx-a11y/label-has-associated-control */}
          <label
            htmlFor="show-viewed"
            className="cursor-pointer pl-1.5 leading-none select-none"
          >
            Показывать просмотренные
          </label>
        </div>
      </nav>

      <div className="mt-10 mb-10 grid grid-cols-2 gap-6">
        {data?.movies.map((movie) => (
          <MovieLargeSnippet key={movie.id} movie={movie} />
        ))}
      </div>
    </main>
  );
};

export default MoviesPage;
