import http from "node:http";

import { getBody } from "../_server.types.js";
import { inviteRepository } from "../dependencies.js";

export async function POST(
  req: http.IncomingMessage,
  res: http.ServerResponse,
): Promise<void> {
  if (req.headers["content-type"] !== "application/json") {
    res.statusCode = 400;
    res.write("Bad Request");
    res.end();

    return;
  }

  const rawBody = await getBody(req);
  let body: unknown;

  try {
    body = JSON.parse(rawBody);
  } catch {
    // Do nothing
  }

  const options = parseOptions(body);

  if (!options) {
    res.statusCode = 400;
    res.write("Bad Request");
    res.end();

    return;
  }

  await inviteRepository.set({
    kinopoiskUrl: options.kinopoiskUrl,
    email: options.email,
    name: options.name,
  });

  res.end();
}

function parseOptions(
  body: unknown,
): { kinopoiskUrl: string; email: string; name: string } | null {
  if (
    !body ||
    typeof body !== "object" ||
    !("email" in body) ||
    !("name" in body) ||
    !("kinopoiskUrl" in body) ||
    typeof body.email !== "string" ||
    typeof body.name !== "string" ||
    typeof body.kinopoiskUrl !== "string"
  ) {
    return null;
  }

  return {
    kinopoiskUrl: body.kinopoiskUrl,
    email: body.email,
    name: body.name,
  };
}
