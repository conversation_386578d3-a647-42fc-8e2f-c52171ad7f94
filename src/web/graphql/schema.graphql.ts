import gql from "graphql-tag";

export default gql`
  scalar DateTime
  scalar Void

  schema {
    query: Query
    mutation: Mutation
  }

  type Query {
    feed: [FeedItem!]!
    genres(sort: GenreSort, limit: Int): [Genre!]!
    me: Account!
    movie(kinopoiskId: ID, slug: String): Movie
    movieViews(
      userSlug: ID
      kinopoiskId: ID
      decade: ID
      director: ID
      genre: ID
    ): [MovieView!]!
    movieViewStats(userSlug: ID, kinopoiskId: ID): MovieViewStats!
    movieViewStatsByDecade(
      userSlug: ID
      kinopoiskId: ID
    ): [MovieViewByDecadeStats!]!
    movieViewStatsByDirector(
      userSlug: ID
      kinopoiskId: ID
      sort: AggregatedMoviesSort!
    ): [MovieViewByDirectorStats!]!
    movieViewStatsByGenre(
      userSlug: ID
      kinopoiskId: ID
      sort: AggregatedMoviesSort!
    ): [MovieViewStatsByGenre!]!
    movies(
      genreSlug: String
      viewed: Boolean
      sort: MovieSort!
      offset: Int
      limit: Int
    ): [Movie!]!
    people: [Person!]!
    person(idOrSlug: String!): Person
    search(text: String!): [SearchResultGroup!]!
    user(userSlug: ID, kinopoiskId: ID): User
    watchlist(userSlug: ID, kinopoiskId: ID): [WatchlistItem!]!

    movieViewsByDecade(
      userSlug: ID
      kinopoiskId: ID
      mark__gte: Int
      mark__lte: Int
    ): [MovieViewDecadeGroup!]!
    movieViewsByDirector(
      userSlug: ID
      kinopoiskId: ID
      sort: AggregatedMoviesSort!
      mark__gte: Int
      mark__lte: Int
    ): [MovieViewDirectorGroup!]!
    movieViewsByGenre(
      userSlug: ID
      kinopoiskId: ID
      sort: AggregatedMoviesSort!
      mark__gte: Int
      mark__lte: Int
    ): [MovieViewGenreGroup!]!
  }

  type Mutation {
    addToWatchlist(movieId: ID!): Void
    trackMovieView(movieId: ID!): Void
    removeFromWatchlist(movieId: ID!): Void
  }

  type Account {
    id: ID!
    name: String!
    slug: String!
    kinopoiskUrl: String!
    isDemoAccount: Boolean!
  }

  enum AggregatedMoviesSort {
    TOTAL_COUNT
    MARKS_OLYMPLIC_COUNT
  }

  enum GenreSort {
    DEFAULT
    RANDOM
  }

  type Decade {
    id: ID!
    label: String!
  }

  type Duration {
    minutes: Int!
  }

  union FeedItem = UserMark

  type Genre {
    id: ID!
    label: String!
    slug: String!
  }

  union HistoryItem = MovieView | MovieMark

  type Image {
    sizes: [ImageSize!]!
  }

  type ImageSize {
    height: Int!
    url: String!
    width: Int!
  }

  type Movie {
    id: ID!
    kinopoiskId: ID!
    slug: String
    title: String!
    logline: String
    directors: [Person!]!
    duration: Duration
    topPositionAllTime: Int
    goodMarksPercentage: Int
    bestMarksPercentage: Int
    genres: [Genre!]!
    images: [Image!]!
    year: Int
    links: MovieLinks!
    watchlistItem: WatchlistItem
    currentMark: MovieMark
    history: [HistoryItem!]!
    friendMovieMarksCount: [Int!]!
    viewed: Boolean!
    canTrackViewAgain: Boolean!
    subtitle: String
  }

  type MovieLinks {
    kinopoisk: String!
    wikipediaRu: String
    wikipediaEn: String
  }

  type MovieSearchResult {
    relevance: Float!
    movie: Movie!
  }

  type MovieMark {
    timestamp: DateTime!
    mark: Int!
    movie: Movie!
    movieId: ID!
  }

  type MovieView {
    timestamp: DateTime!
    mark: Int
    movie: Movie!
    movieId: ID!
  }

  type MovieViewDecadeGroup {
    decade: Decade!
    totalCount: Int!
    bestMarksCount: Int!
    goodMarksCount: Int!
    badMarksCount: Int!
    views: [MovieView!]!
  }

  type MovieViewDirectorGroup {
    director: Person!
    totalCount: Int!
    bestMarksCount: Int!
    goodMarksCount: Int!
    badMarksCount: Int!
    views: [MovieView!]!
  }

  type MovieViewGenreGroup {
    genre: Genre!
    totalCount: Int!
    bestMarksCount: Int!
    goodMarksCount: Int!
    badMarksCount: Int!
    views: [MovieView!]!
  }

  type MovieViewStats {
    totalCount: Int!
    marksCount: [Int!]!
  }

  type MovieViewByDecadeStats {
    decade: Decade!
    totalCount: Int!
    marksCount: [Int!]!
  }

  type MovieViewStatsByGenre {
    genre: Genre!
    totalCount: Int!
    marksCount: [Int!]!
  }

  type MovieViewByDirectorStats {
    person: Person!
    totalCount: Int!
    marksCount: [Int!]!
  }

  enum MovieSort {
    CHRONOLOGICAL
    REVERSE_CHRONOLOGICAL
    BEST_FIRST
    RANDOM
  }

  type Person {
    fullName: String!
    lastName: String!
    id: ID!
    slug: String!
    images: [Image!]!
    movies(sort: MovieSort!, limit: Int): [Movie!]!
    links: PersonLinks!
  }

  type PersonLinks {
    wikipediaRu: String
    wikipediaEn: String
  }

  type PersonSearchResult {
    relevance: Float!
    person: Person!
  }

  union SearchResult = MovieSearchResult | PersonSearchResult

  type SearchResultGroup {
    label: String!
    results(text: String!): [SearchResult!]!
  }

  type User {
    id: ID!
    name: String!
    kinopoiskId: ID!
    slug: String
  }

  type UserMark {
    date: String!
    mark: Int
    movie: Movie!
    user: User!
  }

  type WatchlistItem {
    timestamp: DateTime!
    mark: Int
    movie: Movie!
  }
`;
