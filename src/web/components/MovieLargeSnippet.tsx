import classNames from "classnames";
import * as React from "react";

import { FragmentType, graphql, useFragment } from "../graphql/client.js";
import Join from "./Join.js";
import { useRouter } from "./Router.js";

// eslint-disable-next-line @typescript-eslint/naming-convention
export const MovieLargeSnippet_MovieFragment = graphql(/* GraphQL */ `
  fragment MovieLargeSnippet_MovieFragment on Movie {
    id
    kinopoiskId
    slug
    title
    directors {
      fullName
    }
    goodMarksPercentage
    bestMarksPercentage
    logline
    viewed
    currentMark {
      mark
    }
    topPositionAllTime
    images {
      sizes {
        height
        url
        width
      }
    }
    year
  }
`);

// eslint-disable-next-line @typescript-eslint/naming-convention
export const MovieLargeSnippet_UserFragment = graphql(/* GraphQL */ `
  fragment MovieLargeSnippet_UserFragment on User {
    name
    kinopoiskId
    slug
  }
`);

interface Props {
  movie: FragmentType<typeof MovieLargeSnippet_MovieFragment>;
  aux?: {
    user: FragmentType<typeof MovieLargeSnippet_UserFragment>;
    mark?: number | null;
    type: "friend";
  };
  personSlug?: string;
}

const DIRECTORS_DISPLAY_COUNT_LIMIT = 3;

const MovieLargeSnippet: React.FC<Props> = ({
  movie: movieFragment,
  aux,
  personSlug,
}) => {
  const router = useRouter();
  const movie = useFragment(MovieLargeSnippet_MovieFragment, movieFragment);
  const user = useFragment(MovieLargeSnippet_UserFragment, aux?.user);

  return (
    <div>
      <a
        className="text-2xl leading-none font-bold text-indigo-700 transition-colors hover:text-indigo-500 hover:transition-none"
        href={
          personSlug
            ? movie.slug
              ? router.stringify("/people/:personSlug/movies/:movieSlug", {
                  personSlug,
                  movieSlug: movie.slug,
                })
              : router.stringify(
                  "/people/:personSlug/movies/by-kinopoisk-id/:kinopoiskId",
                  {
                    personSlug,
                    kinopoiskId: movie.kinopoiskId,
                  },
                )
            : movie.slug
            ? router.stringify("/movies/:movieSlug", {
                movieSlug: movie.slug,
              })
            : router.stringify("/movies/by-kinopoisk-id/:kinopoiskId", {
                kinopoiskId: movie.kinopoiskId,
              })
        }
      >
        <figure className="relative">
          {movie.images.length > 0 ? (
            <img
              className="aspect-[1280/720]"
              alt={movie.title}
              src={movie.images[0].sizes[0].url}
              width={movie.images[0].sizes[0].width}
              height={movie.images[0].sizes[0].height}
            />
          ) : (
            <div className="aspect-[1280/720] w-full bg-zinc-100" />
          )}

          <div className="pointer-events-none absolute bottom-[-2px] left-0 flex space-x-2 overflow-hidden rounded-tr bg-white pt-1 pr-2 text-sm text-black empty:hidden">
            {movie.topPositionAllTime ? (
              <div>№&nbsp;{movie.topPositionAllTime}</div>
            ) : null}

            {movie.bestMarksPercentage ? (
              <div className="text-pink-700">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  className="mr-0.5 inline h-[0.8lh] w-[0.8lh] translate-y-[-1px] align-middle"
                >
                  <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                  <path d="M6.979 3.074a6 6 0 0 1 4.988 1.425l.037 .033l.034 -.03a6 6 0 0 1 4.733 -1.44l.246 .036a6 6 0 0 1 3.364 10.008l-.18 .185l-.048 .041l-7.45 7.379a1 1 0 0 1 -1.313 .082l-.094 -.082l-7.493 -7.422a6 6 0 0 1 3.176 -10.215z" />
                </svg>
                {movie.bestMarksPercentage}%
              </div>
            ) : null}

            {movie.goodMarksPercentage != null ? (
              <div
                className={classNames({
                  "text-indigo-700": movie.goodMarksPercentage >= 65,
                  "text-zinc-500": movie.goodMarksPercentage < 65,
                })}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  className="mr-0.5 inline h-[0.8lh] w-[0.8lh] translate-y-[-1px]"
                >
                  <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                  <path d="M13 3a3 3 0 0 1 2.995 2.824l.005 .176v4h2a3 3 0 0 1 2.98 2.65l.015 .174l.005 .176l-.02 .196l-1.006 5.032c-.381 1.626 -1.502 2.796 -2.81 2.78l-.164 -.008h-8a1 1 0 0 1 -.993 -.883l-.007 -.117l.001 -9.536a1 1 0 0 1 .5 -.865a2.998 2.998 0 0 0 1.492 -2.397l.007 -.202v-1a3 3 0 0 1 3 -3z" />
                  <path d="M5 10a1 1 0 0 1 .993 .883l.007 .117v9a1 1 0 0 1 -.883 .993l-.117 .007h-1a2 2 0 0 1 -1.995 -1.85l-.005 -.15v-7a2 2 0 0 1 1.85 -1.995l.15 -.005h1z" />
                </svg>
                {movie.goodMarksPercentage}%
              </div>
            ) : null}
          </div>
        </figure>

        <h3 className="inline-block pt-2">{movie.title}</h3>
      </a>

      {movie.logline ? <p className="mb-2">{movie.logline}</p> : null}

      <div className="mt-0.5 space-x-3">
        {aux?.type === "friend" ? (
          <span>
            <span
              className={classNames(
                "mr-0.5 inline-block w-[1lh] rounded-full text-center tracking-tighter",
                {
                  "bg-pink-700 text-white":
                    aux.mark && aux.mark >= 9 && aux.mark <= 10,
                  "bg-indigo-700 text-white":
                    aux.mark && aux.mark >= 7 && aux.mark <= 8,
                  "bg-zinc-300 text-zinc-950": aux.mark && aux.mark <= 6,
                },
              )}
            >
              {aux.mark ? (
                <span
                  className={classNames({
                    "text-sm": aux.mark === 10,
                  })}
                >
                  {aux.mark}
                </span>
              ) : (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 16 16"
                  className="inline-block h-[1lh] w-[1lh] align-text-top"
                >
                  <path
                    d="M8 12.5C3 12.5.3 8.4.2 8.3L0 8l.1-.3C.2 7.6 2.5 3.5 8 3.5s7.8 4.1 7.8 4.3l.2.3-.2.2c-.1.2-2.8 4.2-7.8 4.2zM1.2 8c.7.8 3.1 3.5 6.8 3.5 3.8 0 6.1-2.7 6.8-3.5-.6-.9-2.6-3.5-6.8-3.5-4.2 0-6.2 2.6-6.8 3.5z"
                    stroke="none"
                  />
                  <path
                    d="M8 10.5c-1.9 0-3.5-1.6-3.5-3.5S6.1 3.5 8 3.5s3.5 1.6 3.5 3.5-1.6 3.5-3.5 3.5zm0-6C6.6 4.5 5.5 5.6 5.5 7S6.6 9.5 8 9.5s2.5-1.1 2.5-2.5S9.4 4.5 8 4.5z"
                    stroke="none"
                  />
                  <circle cx="6.7" cy="6.5" r="1.5" />
                </svg>
              )}
            </span>{" "}
            <a
              href={
                user!.slug
                  ? router.stringify("/:user", {
                      user: encodeURIComponent(user!.slug),
                    })
                  : router.stringify("/user/by-kinopoisk-id/:kinopoiskId", {
                      kinopoiskId: encodeURIComponent(user!.kinopoiskId),
                    })
              }
              className="transition-colors hover:text-indigo-500 hover:transition-none"
            >
              {user!.name}
            </a>
          </span>
        ) : null}

        {movie.viewed ? (
          <span>
            <span
              className={classNames(
                "mr-0.5 inline-block w-[1lh] rounded-full text-center tracking-tighter",
                {
                  "bg-pink-700 text-white":
                    movie.currentMark &&
                    movie.currentMark.mark >= 9 &&
                    movie.currentMark.mark <= 10,
                  "bg-indigo-700 text-white":
                    movie.currentMark &&
                    movie.currentMark.mark >= 7 &&
                    movie.currentMark.mark <= 8,
                  "bg-zinc-300 text-zinc-950":
                    movie.currentMark && movie.currentMark.mark <= 6,
                },
              )}
            >
              {movie.currentMark?.mark ? (
                <span
                  className={classNames({
                    "text-sm": movie.currentMark.mark === 10,
                  })}
                >
                  {movie.currentMark.mark}
                </span>
              ) : (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 16 16"
                  className="inline-block h-[1lh] w-[1lh] align-text-top"
                >
                  <path
                    d="M8 12.5C3 12.5.3 8.4.2 8.3L0 8l.1-.3C.2 7.6 2.5 3.5 8 3.5s7.8 4.1 7.8 4.3l.2.3-.2.2c-.1.2-2.8 4.2-7.8 4.2zM1.2 8c.7.8 3.1 3.5 6.8 3.5 3.8 0 6.1-2.7 6.8-3.5-.6-.9-2.6-3.5-6.8-3.5-4.2 0-6.2 2.6-6.8 3.5z"
                    stroke="none"
                  />
                  <path
                    d="M8 10.5c-1.9 0-3.5-1.6-3.5-3.5S6.1 3.5 8 3.5s3.5 1.6 3.5 3.5-1.6 3.5-3.5 3.5zm0-6C6.6 4.5 5.5 5.6 5.5 7S6.6 9.5 8 9.5s2.5-1.1 2.5-2.5S9.4 4.5 8 4.5z"
                    stroke="none"
                  />
                  <circle cx="6.7" cy="6.5" r="1.5" />
                </svg>
              )}
            </span>{" "}
            Моя оценка
          </span>
        ) : null}

        {movie.directors.length > 0 || movie.year ? (
          <Join delimiter={`,\u00A0`}>
            {movie.directors.length > DIRECTORS_DISPLAY_COUNT_LIMIT
              ? movie.directors
                  .slice(0, DIRECTORS_DISPLAY_COUNT_LIMIT - 1)
                  .map((director) => director.fullName)
                  .join(", ") +
                " и ещё " +
                String(
                  movie.directors.length - (DIRECTORS_DISPLAY_COUNT_LIMIT - 1),
                )
              : movie.directors.length > 0
              ? movie.directors.map((director) => director.fullName).join(", ")
              : null}
            {movie.year ?? null}
          </Join>
        ) : null}
      </div>
    </div>
  );
};

export default MovieLargeSnippet;
